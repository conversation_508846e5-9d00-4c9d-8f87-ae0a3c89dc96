import 'package:flutter/foundation.dart';

class AppConfig {
  // App Information
  static const String appVersion = '1.0.0';
  static const int buildNumber = 1;
  
  // Network Configuration
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // Cache Configuration
  static const Duration cacheExpiration = Duration(hours: 24);
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  
  // UI Configuration
  static const double defaultPadding = 16.0;
  static const double defaultBorderRadius = 8.0;
  static const double defaultElevation = 2.0;
  
  // Animation Configuration
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);
  static const Duration shortAnimationDuration = Duration(milliseconds: 150);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // Pagination Configuration
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Security Configuration
  static const Duration sessionTimeout = Duration(minutes: 30);
  static const int maxLoginAttempts = 5;
  static const Duration lockoutDuration = Duration(minutes: 15);
  
  // Logging Configuration
  static bool get enableLogging => kDebugMode;
  static bool get enableCrashReporting => !kDebugMode;
  
  // Feature Flags (can be overridden by customer config)
  static const Map<String, bool> defaultFeatures = {
    'biometricAuth': true,
    'pushNotifications': true,
    'offlineMode': true,
    'darkMode': true,
    'multiLanguage': true,
    'advancedCharting': true,
    'cryptoTrading': true,
    'socialTrading': true,
    'paperTrading': true,
    'newsIntegration': true,
  };
  
  // Supported Languages
  static const List<String> supportedLanguages = [
    'en', // English
    'es', // Spanish
    'fr', // French
    'zh', // Chinese
    'ja', // Japanese
    'ko', // Korean
    'de', // German
    'it', // Italian
    'pt', // Portuguese
    'ru', // Russian
  ];
  
  // Supported Currencies
  static const List<String> supportedCurrencies = [
    'USD', 'EUR', 'GBP', 'JPY', 'CHF', 'CAD', 'AUD', 'NZD',
    'CNY', 'KRW', 'SGD', 'HKD', 'INR', 'BRL', 'MXN', 'ZAR',
  ];
  
  // Trading Configuration
  static const Map<String, dynamic> tradingConfig = {
    'minOrderAmount': 1.0,
    'maxOrderAmount': 1000000.0,
    'defaultLeverage': 1.0,
    'maxLeverage': 100.0,
    'supportedOrderTypes': [
      'market',
      'limit',
      'stop',
      'stopLimit',
      'trailingStop',
    ],
    'supportedTimeframes': [
      '1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w', '1M',
    ],
  };
  
  // Chart Configuration
  static const Map<String, dynamic> chartConfig = {
    'defaultTimeframe': '1h',
    'maxDataPoints': 1000,
    'refreshInterval': Duration(seconds: 5),
    'supportedIndicators': [
      'SMA', 'EMA', 'RSI', 'MACD', 'Bollinger Bands',
      'Stochastic', 'Williams %R', 'CCI', 'ADX', 'Ichimoku',
    ],
  };
  
  // Notification Configuration
  static const Map<String, dynamic> notificationConfig = {
    'enablePushNotifications': true,
    'enableEmailNotifications': true,
    'enableSMSNotifications': false,
    'defaultNotificationTypes': [
      'priceAlerts',
      'orderExecutions',
      'accountUpdates',
      'marketNews',
      'systemMaintenance',
    ],
  };
}
