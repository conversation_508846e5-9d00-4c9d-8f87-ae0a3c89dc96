enum Flavor {
  development,
  staging,
  production,
  customerA,
  customerB,
  customerC,
}

class FlavorConfig {
  static FlavorConfig? _instance;
  static FlavorConfig get instance => _instance!;
  
  final Flavor flavor;
  final String appName;
  final String baseUrl;
  final String customerId;
  final bool debugMode;
  final Map<String, dynamic> customerConfig;

  FlavorConfig._internal({
    required this.flavor,
    required this.appName,
    required this.baseUrl,
    required this.customerId,
    required this.debugMode,
    required this.customerConfig,
  });

  static void setFlavor(Flavor flavor) {
    _instance = FlavorConfig._internal(
      flavor: flavor,
      appName: _getAppName(flavor),
      baseUrl: _getBaseUrl(flavor),
      customerId: _getCustomerId(flavor),
      debugMode: _getDebugMode(flavor),
      customerConfig: _getCustomerConfig(flavor),
    );
  }

  static String _getAppName(Flavor flavor) {
    switch (flavor) {
      case Flavor.development:
        return 'Phoenix Trade (Dev)';
      case Flavor.staging:
        return 'Phoenix Trade (Staging)';
      case Flavor.production:
        return 'Phoenix Trade';
      case Flavor.customerA:
        return 'TradePro Alpha';
      case Flavor.customerB:
        return 'MarketMaster Beta';
      case Flavor.customerC:
        return 'InvestSmart Gamma';
    }
  }

  static String _getBaseUrl(Flavor flavor) {
    switch (flavor) {
      case Flavor.development:
        return 'https://dev-api.phoenixtrade.com';
      case Flavor.staging:
        return 'https://staging-api.phoenixtrade.com';
      case Flavor.production:
        return 'https://api.phoenixtrade.com';
      case Flavor.customerA:
        return 'https://api.tradepro-alpha.com';
      case Flavor.customerB:
        return 'https://api.marketmaster-beta.com';
      case Flavor.customerC:
        return 'https://api.investsmart-gamma.com';
    }
  }

  static String _getCustomerId(Flavor flavor) {
    switch (flavor) {
      case Flavor.development:
      case Flavor.staging:
      case Flavor.production:
        return 'default';
      case Flavor.customerA:
        return 'customer_a';
      case Flavor.customerB:
        return 'customer_b';
      case Flavor.customerC:
        return 'customer_c';
    }
  }

  static bool _getDebugMode(Flavor flavor) {
    switch (flavor) {
      case Flavor.development:
      case Flavor.staging:
        return true;
      case Flavor.production:
      case Flavor.customerA:
      case Flavor.customerB:
      case Flavor.customerC:
        return false;
    }
  }

  static Map<String, dynamic> _getCustomerConfig(Flavor flavor) {
    switch (flavor) {
      case Flavor.development:
      case Flavor.staging:
      case Flavor.production:
        return {
          'supportEmail': '<EMAIL>',
          'supportPhone': '******-PHOENIX',
          'features': {
            'advancedCharting': true,
            'cryptoTrading': true,
            'socialTrading': true,
          },
        };
      case Flavor.customerA:
        return {
          'supportEmail': '<EMAIL>',
          'supportPhone': '******-TRADEPRO',
          'features': {
            'advancedCharting': true,
            'cryptoTrading': false,
            'socialTrading': true,
          },
        };
      case Flavor.customerB:
        return {
          'supportEmail': '<EMAIL>',
          'supportPhone': '******-MARKET',
          'features': {
            'advancedCharting': false,
            'cryptoTrading': true,
            'socialTrading': false,
          },
        };
      case Flavor.customerC:
        return {
          'supportEmail': '<EMAIL>',
          'supportPhone': '******-INVEST',
          'features': {
            'advancedCharting': true,
            'cryptoTrading': true,
            'socialTrading': true,
          },
        };
    }
  }

  bool hasFeature(String featureName) {
    final features = customerConfig['features'] as Map<String, dynamic>?;
    return features?[featureName] ?? false;
  }

  String get supportEmail => customerConfig['supportEmail'] as String;
  String get supportPhone => customerConfig['supportPhone'] as String;
}
