import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'app.dart';
import 'config/flavor_config.dart';
import 'core/dependency_injection/injection_container.dart' as di;
import 'core/utils/logger.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize logger
  AppLogger.init();
  
  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  
  // Initialize dependency injection
  await di.init();
  
  // Set default flavor (can be overridden by customer-specific main files)
  FlavorConfig.setFlavor(Flavor.development);
  
  runApp(const PhoenixTradeApp());
}

class PhoenixTradeApp extends StatelessWidget {
  const PhoenixTradeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812), // iPhone X design size
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return BlocProvider(
          create: (context) => di.sl<AppBloc>(),
          child: const App(),
        );
      },
    );
  }
}

// Placeholder for AppBloc - will be implemented later
class AppBloc extends Cubit<AppState> {
  AppBloc() : super(AppInitial());
}

abstract class AppState {}
class AppInitial extends AppState {}
