class AppConstants {
  // App Information
  static const String appName = 'Phoenix Trade';
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';
  
  // Storage Keys
  static const String authTokenKey = 'auth_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userIdKey = 'user_id';
  static const String userEmailKey = 'user_email';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language_code';
  static const String customerIdKey = 'customer_id';
  static const String biometricEnabledKey = 'biometric_enabled';
  static const String notificationsEnabledKey = 'notifications_enabled';
  static const String firstLaunchKey = 'first_launch';
  static const String onboardingCompletedKey = 'onboarding_completed';
  
  // Cache Keys
  static const String userProfileCacheKey = 'user_profile';
  static const String portfolioCacheKey = 'portfolio';
  static const String watchlistCacheKey = 'watchlist';
  static const String marketDataCacheKey = 'market_data';
  static const String newsCacheKey = 'news';
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 150);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  static const Duration splashDuration = Duration(seconds: 3);
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double extraLargePadding = 32.0;
  
  static const double defaultBorderRadius = 8.0;
  static const double smallBorderRadius = 4.0;
  static const double largeBorderRadius = 16.0;
  static const double circularBorderRadius = 50.0;
  
  static const double defaultElevation = 2.0;
  static const double mediumElevation = 4.0;
  static const double highElevation = 8.0;
  
  static const double appBarHeight = 56.0;
  static const double bottomNavHeight = 60.0;
  static const double tabBarHeight = 48.0;
  
  // Text Sizes
  static const double smallTextSize = 12.0;
  static const double normalTextSize = 14.0;
  static const double mediumTextSize = 16.0;
  static const double largeTextSize = 18.0;
  static const double extraLargeTextSize = 24.0;
  static const double titleTextSize = 20.0;
  static const double headlineTextSize = 28.0;
  
  // Icon Sizes
  static const double smallIconSize = 16.0;
  static const double normalIconSize = 24.0;
  static const double mediumIconSize = 32.0;
  static const double largeIconSize = 48.0;
  static const double extraLargeIconSize = 64.0;
  
  // Button Heights
  static const double smallButtonHeight = 32.0;
  static const double normalButtonHeight = 48.0;
  static const double largeButtonHeight = 56.0;
  
  // Form Constants
  static const double textFieldHeight = 56.0;
  static const double textFieldBorderRadius = 8.0;
  static const int maxUsernameLength = 20;
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  
  // Trading Constants
  static const double minTradeAmount = 1.0;
  static const double maxTradeAmount = 1000000.0;
  static const double defaultLeverage = 1.0;
  static const double maxLeverage = 100.0;
  static const int maxDecimalPlaces = 8;
  static const int priceDecimalPlaces = 2;
  
  // Chart Constants
  static const int maxChartDataPoints = 1000;
  static const Duration chartRefreshInterval = Duration(seconds: 5);
  static const Duration realtimeUpdateInterval = Duration(seconds: 1);
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  static const int minPageSize = 10;
  
  // Timeouts
  static const Duration networkTimeout = Duration(seconds: 30);
  static const Duration shortTimeout = Duration(seconds: 10);
  static const Duration longTimeout = Duration(minutes: 2);
  static const Duration sessionTimeout = Duration(minutes: 30);
  
  // Retry Configuration
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 1);
  static const Duration exponentialBackoffBase = Duration(seconds: 2);
  
  // Security
  static const int maxLoginAttempts = 5;
  static const Duration lockoutDuration = Duration(minutes: 15);
  static const Duration tokenRefreshThreshold = Duration(minutes: 5);
  
  // File Sizes
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int maxDocumentSize = 10 * 1024 * 1024; // 10MB
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  
  // Supported File Types
  static const List<String> supportedImageTypes = [
    'jpg', 'jpeg', 'png', 'gif', 'webp'
  ];
  
  static const List<String> supportedDocumentTypes = [
    'pdf', 'doc', 'docx', 'txt'
  ];
  
  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm:ss';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  static const String displayDateFormat = 'MMM dd, yyyy';
  static const String displayDateTimeFormat = 'MMM dd, yyyy HH:mm';
  
  // Regex Patterns
  static const String emailPattern = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
  static const String phonePattern = r'^\+?[\d\s\-\(\)]{10,}$';
  static const String passwordPattern = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$';
  static const String usernamePattern = r'^[a-zA-Z0-9_]{3,20}$';
  static const String numericPattern = r'^[0-9]+(\.[0-9]+)?$';
  
  // URLs
  static const String privacyPolicyUrl = 'https://phoenixtrade.com/privacy';
  static const String termsOfServiceUrl = 'https://phoenixtrade.com/terms';
  static const String supportUrl = 'https://phoenixtrade.com/support';
  static const String faqUrl = 'https://phoenixtrade.com/faq';
  
  // Social Media
  static const String twitterUrl = 'https://twitter.com/phoenixtrade';
  static const String facebookUrl = 'https://facebook.com/phoenixtrade';
  static const String linkedinUrl = 'https://linkedin.com/company/phoenixtrade';
  static const String youtubeUrl = 'https://youtube.com/phoenixtrade';
  
  // Error Messages
  static const String genericErrorMessage = 'An unexpected error occurred. Please try again.';
  static const String networkErrorMessage = 'Please check your internet connection and try again.';
  static const String serverErrorMessage = 'Server error. Please try again later.';
  static const String validationErrorMessage = 'Please check your input and try again.';
  static const String authErrorMessage = 'Authentication failed. Please login again.';
  static const String permissionErrorMessage = 'You don\'t have permission to perform this action.';
  
  // Success Messages
  static const String loginSuccessMessage = 'Login successful!';
  static const String logoutSuccessMessage = 'Logout successful!';
  static const String registrationSuccessMessage = 'Registration successful!';
  static const String profileUpdateSuccessMessage = 'Profile updated successfully!';
  static const String passwordChangeSuccessMessage = 'Password changed successfully!';
  
  // Feature Flags
  static const String biometricAuthFeature = 'biometric_auth';
  static const String pushNotificationsFeature = 'push_notifications';
  static const String darkModeFeature = 'dark_mode';
  static const String advancedChartingFeature = 'advanced_charting';
  static const String cryptoTradingFeature = 'crypto_trading';
  static const String socialTradingFeature = 'social_trading';
  static const String paperTradingFeature = 'paper_trading';
  static const String newsIntegrationFeature = 'news_integration';
}
