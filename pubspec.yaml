name: phoenixtradeapp
description: A cross-platform trading application supporting multiple customers, themes, and languages.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  
  # State Management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  
  # Dependency Injection
  get_it: ^7.6.4
  injectable: ^2.3.2
  
  # Network
  dio: ^5.3.2
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # UI & Theming
  flutter_screenutil: ^5.9.0
  cached_network_image: ^3.3.0
  flutter_svg: ^2.0.7
  
  # Utilities
  intl: ^0.18.1
  logger: ^2.0.2+1
  connectivity_plus: ^5.0.1
  package_info_plus: ^4.2.0
  device_info_plus: ^9.1.0
  
  # Development
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.3.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  
  # Code Generation
  build_runner: ^2.4.7
  injectable_generator: ^2.4.1
  retrofit_generator: ^8.0.4
  json_serializable: ^6.7.1
  hive_generator: ^2.0.1
  
  # Testing
  mockito: ^5.4.2
  bloc_test: ^9.1.4

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/common/
    - assets/images/customer_a/
    - assets/images/customer_b/
    - assets/images/customer_c/
    - assets/icons/common/
    - assets/icons/customer_specific/
    - assets/fonts/common/
    - assets/fonts/customer_specific/

  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/common/Roboto-Regular.ttf
        - asset: assets/fonts/common/Roboto-Bold.ttf
          weight: 700

flutter_intl:
  enabled: true
  class_name: S
  main_locale: en
  arb_dir: lib/shared/localization/l10n
  output_dir: lib/shared/localization

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/common/app_icon.png"
  min_sdk_android: 21

flutter_native_splash:
  color: "#ffffff"
  image: assets/images/common/splash_logo.png
  android_12:
    image: assets/images/common/splash_logo.png
    color: "#ffffff"
