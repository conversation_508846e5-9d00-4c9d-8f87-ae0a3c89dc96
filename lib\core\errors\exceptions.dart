/// Base exception class for the application
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic details;

  const AppException(this.message, {this.code, this.details});

  @override
  String toString() => 'AppException: $message';
}

/// Server-related exceptions
class ServerException extends AppException {
  const ServerException(super.message, {super.code, super.details});
}

/// Network-related exceptions
class NetworkException extends AppException {
  const NetworkException(super.message, {super.code, super.details});
}

/// Cache-related exceptions
class CacheException extends AppException {
  const CacheException(super.message, {super.code, super.details});
}

/// Authentication-related exceptions
class AuthException extends AppException {
  const AuthException(super.message, {super.code, super.details});
}

/// Validation-related exceptions
class ValidationException extends AppException {
  final Map<String, List<String>>? fieldErrors;
  
  const ValidationException(
    super.message, {
    super.code,
    super.details,
    this.fieldErrors,
  });
}

/// Permission-related exceptions
class PermissionException extends AppException {
  const PermissionException(super.message, {super.code, super.details});
}

/// Business logic exceptions
class BusinessLogicException extends AppException {
  const BusinessLogicException(super.message, {super.code, super.details});
}

/// Timeout exceptions
class TimeoutException extends AppException {
  const TimeoutException(super.message, {super.code, super.details});
}

/// Not found exceptions
class NotFoundException extends AppException {
  const NotFoundException(super.message, {super.code, super.details});
}

/// Conflict exceptions (409)
class ConflictException extends AppException {
  const ConflictException(super.message, {super.code, super.details});
}

/// Rate limit exceptions (429)
class RateLimitException extends AppException {
  final DateTime? retryAfter;
  
  const RateLimitException(
    super.message, {
    super.code,
    super.details,
    this.retryAfter,
  });
}

/// Maintenance mode exceptions
class MaintenanceException extends AppException {
  final DateTime? estimatedEndTime;
  
  const MaintenanceException(
    super.message, {
    super.code,
    super.details,
    this.estimatedEndTime,
  });
}

/// Feature not available exceptions
class FeatureNotAvailableException extends AppException {
  final String featureName;
  
  const FeatureNotAvailableException(
    this.featureName,
    super.message, {
    super.code,
    super.details,
  });
}

/// Customer-specific exceptions
class CustomerConfigException extends AppException {
  final String customerId;
  
  const CustomerConfigException(
    this.customerId,
    super.message, {
    super.code,
    super.details,
  });
}

/// Trading-specific exceptions
class TradingException extends AppException {
  const TradingException(super.message, {super.code, super.details});
}

class InsufficientFundsException extends TradingException {
  final double availableBalance;
  final double requiredAmount;
  
  const InsufficientFundsException(
    this.availableBalance,
    this.requiredAmount,
    super.message, {
    super.code,
    super.details,
  });
}

class MarketClosedException extends TradingException {
  final String marketName;
  final DateTime? nextOpenTime;
  
  const MarketClosedException(
    this.marketName,
    super.message, {
    this.nextOpenTime,
    super.code,
    super.details,
  });
}

class InvalidOrderException extends TradingException {
  final String orderType;
  final Map<String, dynamic>? orderDetails;
  
  const InvalidOrderException(
    this.orderType,
    super.message, {
    this.orderDetails,
    super.code,
    super.details,
  });
}
