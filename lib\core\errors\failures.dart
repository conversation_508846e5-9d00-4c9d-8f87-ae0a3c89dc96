import 'package:equatable/equatable.dart';

/// Base failure class for error handling in the presentation layer
abstract class Failure extends Equatable {
  final String message;
  final String? code;
  final dynamic details;

  const Failure(this.message, {this.code, this.details});

  @override
  List<Object?> get props => [message, code, details];
}

/// Server failure
class ServerFailure extends Failure {
  const ServerFailure(super.message, {super.code, super.details});
}

/// Network failure
class NetworkFailure extends Failure {
  const NetworkFailure(super.message, {super.code, super.details});
}

/// Cache failure
class CacheFailure extends Failure {
  const CacheFailure(super.message, {super.code, super.details});
}

/// Authentication failure
class AuthFailure extends Failure {
  const AuthFailure(super.message, {super.code, super.details});
}

/// Validation failure
class ValidationFailure extends Failure {
  final Map<String, List<String>>? fieldErrors;
  
  const ValidationFailure(
    super.message, {
    super.code,
    super.details,
    this.fieldErrors,
  });

  @override
  List<Object?> get props => [message, code, details, fieldErrors];
}

/// Permission failure
class PermissionFailure extends Failure {
  const PermissionFailure(super.message, {super.code, super.details});
}

/// Business logic failure
class BusinessLogicFailure extends Failure {
  const BusinessLogicFailure(super.message, {super.code, super.details});
}

/// Timeout failure
class TimeoutFailure extends Failure {
  const TimeoutFailure(super.message, {super.code, super.details});
}

/// Not found failure
class NotFoundFailure extends Failure {
  const NotFoundFailure(super.message, {super.code, super.details});
}

/// Conflict failure
class ConflictFailure extends Failure {
  const ConflictFailure(super.message, {super.code, super.details});
}

/// Rate limit failure
class RateLimitFailure extends Failure {
  final DateTime? retryAfter;
  
  const RateLimitFailure(
    super.message, {
    super.code,
    super.details,
    this.retryAfter,
  });

  @override
  List<Object?> get props => [message, code, details, retryAfter];
}

/// Maintenance failure
class MaintenanceFailure extends Failure {
  final DateTime? estimatedEndTime;
  
  const MaintenanceFailure(
    super.message, {
    super.code,
    super.details,
    this.estimatedEndTime,
  });

  @override
  List<Object?> get props => [message, code, details, estimatedEndTime];
}

/// Feature not available failure
class FeatureNotAvailableFailure extends Failure {
  final String featureName;
  
  const FeatureNotAvailableFailure(
    this.featureName,
    super.message, {
    super.code,
    super.details,
  });

  @override
  List<Object?> get props => [featureName, message, code, details];
}

/// Customer config failure
class CustomerConfigFailure extends Failure {
  final String customerId;
  
  const CustomerConfigFailure(
    this.customerId,
    super.message, {
    super.code,
    super.details,
  });

  @override
  List<Object?> get props => [customerId, message, code, details];
}

/// Trading failure
class TradingFailure extends Failure {
  const TradingFailure(super.message, {super.code, super.details});
}

/// Insufficient funds failure
class InsufficientFundsFailure extends TradingFailure {
  final double availableBalance;
  final double requiredAmount;
  
  const InsufficientFundsFailure(
    this.availableBalance,
    this.requiredAmount,
    super.message, {
    super.code,
    super.details,
  });

  @override
  List<Object?> get props => [availableBalance, requiredAmount, message, code, details];
}

/// Market closed failure
class MarketClosedFailure extends TradingFailure {
  final String marketName;
  final DateTime? nextOpenTime;
  
  const MarketClosedFailure(
    this.marketName,
    super.message, {
    this.nextOpenTime,
    super.code,
    super.details,
  });

  @override
  List<Object?> get props => [marketName, nextOpenTime, message, code, details];
}

/// Invalid order failure
class InvalidOrderFailure extends TradingFailure {
  final String orderType;
  final Map<String, dynamic>? orderDetails;
  
  const InvalidOrderFailure(
    this.orderType,
    super.message, {
    this.orderDetails,
    super.code,
    super.details,
  });

  @override
  List<Object?> get props => [orderType, orderDetails, message, code, details];
}

/// Utility class to convert exceptions to failures
class FailureMapper {
  static Failure mapExceptionToFailure(Exception exception) {
    // This will be implemented based on the specific exceptions
    // For now, return a generic server failure
    return ServerFailure(exception.toString());
  }
}
