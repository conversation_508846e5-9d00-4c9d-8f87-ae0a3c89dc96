import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

class AppLogger {
  static Logger? _logger;
  static Logger get instance => _logger!;

  static void init() {
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: true,
      ),
      level: kDebugMode ? Level.debug : Level.warning,
    );
  }

  static void debug(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger?.d(message, error: error, stackTrace: stackTrace);
  }

  static void info(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger?.i(message, error: error, stackTrace: stackTrace);
  }

  static void warning(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger?.w(message, error: error, stackTrace: stackTrace);
  }

  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger?.e(message, error: error, stackTrace: stackTrace);
  }

  static void fatal(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger?.f(message, error: error, stackTrace: stackTrace);
  }

  // Specific logging methods for different components
  static void network(String message, {String? method, String? url, int? statusCode}) {
    final logMessage = '[$method] $url ${statusCode != null ? '($statusCode)' : ''} - $message';
    info(logMessage);
  }

  static void auth(String message, [dynamic error, StackTrace? stackTrace]) {
    info('[AUTH] $message', error, stackTrace);
  }

  static void trading(String message, [dynamic error, StackTrace? stackTrace]) {
    info('[TRADING] $message', error, stackTrace);
  }

  static void ui(String message, [dynamic error, StackTrace? stackTrace]) {
    debug('[UI] $message', error, stackTrace);
  }

  static void performance(String message, {Duration? duration}) {
    final logMessage = duration != null 
        ? '[PERFORMANCE] $message (${duration.inMilliseconds}ms)'
        : '[PERFORMANCE] $message';
    info(logMessage);
  }
}
